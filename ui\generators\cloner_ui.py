"""
Cloner UI Generator для ClonerPro
Единая система генерации UI для ВСЕХ типов клонеров в стиле ObjectCloner
"""

import bpy

from ...core.services.cloner_scanner import ClonerDiscovery
from ..utils.ui_helpers import display_socket_prop


class UnifiedClonerUI:
    """UI Generator класс для единого UI всех клонеров в стиле ObjectCloner"""
    
    @staticmethod
    def draw_cloner_settings(layout, context, modifier):
        """Отрисовка настроек одного клонера в стиле ObjectCloner"""
        
        # Определяем тип клонера
        cloner_info = ClonerDiscovery.analyze_cloner_modifier(context.active_object, modifier)
        if not cloner_info:
            return
        
        cloner_type = cloner_info.get('cloner_type', 'UNKNOWN')
        
        if cloner_type == "GRID":
            UnifiedClonerUI.draw_cloner_from_config(layout, modifier, "GRID")
        elif cloner_type == "LINEAR":
            UnifiedClonerUI.draw_cloner_from_config(layout, modifier, "LINEAR")
        elif cloner_type == "CIRCLE":
            UnifiedClonerUI.draw_cloner_from_config(layout, modifier, "CIRCLE")
        elif cloner_type == "SPIRAL":
            UnifiedClonerUI.draw_cloner_from_config(layout, modifier, "SPIRAL")
        elif cloner_type == "OBJECT":
            UnifiedClonerUI.draw_object_cloner_settings(layout, modifier)
        elif cloner_type == "SPLINE":
            UnifiedClonerUI.draw_spline_cloner_settings(layout, modifier)
        else:
            layout.label(text=f"Unknown cloner type: {cloner_type}", icon='ERROR')
    
    @staticmethod
    def draw_grid_cloner_settings(layout, modifier):
        """Настройки Grid Cloner в стиле ObjectCloner"""
        
        # Grid Settings
        box = layout.box()
        col = box.column(align=True)
        col.label(text="Grid Settings:", icon='MESH_GRID')
        
        col.separator()
        display_socket_prop(col, modifier, "Count X", text="Count X")
        display_socket_prop(col, modifier, "Count Y", text="Count Y") 
        display_socket_prop(col, modifier, "Count Z", text="Count Z")
        
        col.separator()
        display_socket_prop(col, modifier, "Spacing", text="Spacing")
        
        # Instance Transform
        box = layout.box()
        col = box.column(align=True)
        col.label(text="Instance Transform:", icon='OBJECT_DATA')
        
        col.separator()
        display_socket_prop(col, modifier, "Instance Scale", text="Scale")
        
        col.separator()
        display_socket_prop(col, modifier, "Instance Rotation", text="Rotation")
        
        # Random Transform  
        box = layout.box()
        col = box.column(align=True)
        col.label(text="Random Transform:", icon='FORCE_TURBULENCE')
        
        col.separator()
        display_socket_prop(col, modifier, "Random Position", text="Random Position")
        
        col.separator()
        display_socket_prop(col, modifier, "Random Rotation", text="Random Rotation")
        
        col.separator()
        display_socket_prop(col, modifier, "Random Scale", text="Random Scale")
        
        col.separator()
        display_socket_prop(col, modifier, "Random Seed", text="Random Seed")
        
        # Global Transform
        box = layout.box()
        col = box.column(align=True)
        col.label(text="Global Transform:", icon='WORLD_DATA')
        
        col.separator()
        display_socket_prop(col, modifier, "Global Position", text="Position")
        
        col.separator() 
        display_socket_prop(col, modifier, "Global Rotation", text="Rotation")
    
    @staticmethod
    def draw_linear_cloner_settings(layout, modifier):
        """Настройки Linear Cloner в стиле ObjectCloner"""
        
        # Linear Settings
        box = layout.box()
        col = box.column(align=True)
        col.label(text="Linear Settings:", icon='CURVE_PATH')
        
        col.separator()
        display_socket_prop(col, modifier, "Count", text="Count")
        display_socket_prop(col, modifier, "Offset", text="Offset")
        
        # Gradient Settings
        box = layout.box()
        col = box.column(align=True)
        col.label(text="Gradient Settings:", icon='IPO_LINEAR')
        
        col.separator()
        display_socket_prop(col, modifier, "Scale Start", text="Scale Start")
        display_socket_prop(col, modifier, "Scale End", text="Scale End")
        
        col.separator()
        display_socket_prop(col, modifier, "Rotation Start", text="Rotation Start")
        display_socket_prop(col, modifier, "Rotation End", text="Rotation End")
        
        # Global Transform
        box = layout.box()
        col = box.column(align=True)
        col.label(text="Global Transform:", icon='WORLD_DATA')
        
        col.separator()
        display_socket_prop(col, modifier, "Global Position", text="Position")
        
        col.separator()
        display_socket_prop(col, modifier, "Global Rotation", text="Rotation")
        
        # Random Transform
        UnifiedClonerUI.draw_common_random_transform(layout, modifier)
    
    @staticmethod
    def draw_circle_cloner_settings(layout, modifier):
        """Настройки Circle Cloner в стиле ObjectCloner"""
        
        # Circle Settings
        box = layout.box()
        col = box.column(align=True)
        col.label(text="Circle Settings:", icon='MESH_CIRCLE')
        
        col.separator()
        display_socket_prop(col, modifier, "Count", text="Count")
        display_socket_prop(col, modifier, "Radius", text="Radius")
        display_socket_prop(col, modifier, "Height", text="Height")
        
        # Instance Transform
        box = layout.box()
        col = box.column(align=True)
        col.label(text="Instance Transform:", icon='OBJECT_DATA')
        
        col.separator()
        display_socket_prop(col, modifier, "Instance Scale", text="Scale")
        
        col.separator()
        display_socket_prop(col, modifier, "Instance Rotation", text="Rotation")
        
        # Random Transform
        UnifiedClonerUI.draw_common_random_transform(layout, modifier)

    @staticmethod
    def draw_spiral_cloner_settings(layout, modifier):
        """Настройки Spiral Cloner в стиле ObjectCloner"""

        # Spiral Settings
        box = layout.box()
        col = box.column(align=True)
        col.label(text="Spiral Settings", icon="FORCE_VORTEX")

        # Count
        display_socket_prop(col, modifier, "Count", text="Count")

        # Spiral Parameters
        display_socket_prop(col, modifier, "Turns", text="Turns")
        display_socket_prop(col, modifier, "Height", text="Height")

        # Radius Settings
        display_socket_prop(col, modifier, "Radius", text="Radius")
        display_socket_prop(col, modifier, "Start Radius", text="Start Radius")
        display_socket_prop(col, modifier, "End Radius", text="End Radius")

        # Axis
        display_socket_prop(col, modifier, "Axis", text="Axis")

        # Instance Transform
        UnifiedClonerUI.draw_common_instance_transform(layout, modifier)

        # Random Transform
        UnifiedClonerUI.draw_common_random_transform(layout, modifier)

    @staticmethod
    def draw_object_cloner_settings(layout, modifier):
        """Настройки Object Cloner в стиле ObjectCloner (уже готово)"""
        
        # Source Mode - определяем динамически
        source_mode = UnifiedClonerUI.get_socket_value(modifier, "Source Mode")
        if source_mode is None:
            source_mode = 0
        
        # Source секция
        box = layout.box()
        col = box.column(align=True)
        
        if source_mode == 0:  # Object mode
            col.label(text="Object Cloner Source:", icon='OBJECT_DATA')
            display_socket_prop(col, modifier, "Source Object", text="Source Object")
        else:  # Collection mode
            col.label(text="Collection Cloner Source:", icon='OUTLINER_COLLECTION')
            display_socket_prop(col, modifier, "Source Collection", text="Source Collection")
            
            # Collection Pick Instance
            col.separator()
            display_socket_prop(col, modifier, "Collection Pick Instance", text="Random Mesh Collection")
            
            # Collection Random Seed если включена рандомизация
            random_pick = UnifiedClonerUI.get_socket_value(modifier, "Collection Pick Instance")
            if random_pick is True:
                display_socket_prop(col, modifier, "Collection Random Seed", text="Seed Random")
        
        # Hide Original
        col.separator()
        display_socket_prop(col, modifier, "Hide Original", text="Hide Original Mesh")
        
        # Distribution секция
        box = layout.box()
        col = box.column(align=True)
        col.label(text="Distribution:", icon='PARTICLE_DATA')
        
        # Distribution Mode buttons
        mode_val = UnifiedClonerUI.get_socket_value(modifier, "Distribution Mode")
        if mode_val is None:
            mode_val = 0
            
        row = col.row(align=True)
        
        # Vertices button
        vertices_op = row.operator("clonerpro.set_socket_value", text="Vertices", depress=(mode_val == 0))
        vertices_op.object_name = modifier.id_data.name
        vertices_op.modifier_name = modifier.name  
        vertices_op.socket_name = "Distribution Mode"
        vertices_op.socket_value = "0"
        
        # Edges button  
        edges_op = row.operator("clonerpro.set_socket_value", text="Edges", depress=(mode_val == 1))
        edges_op.object_name = modifier.id_data.name
        edges_op.modifier_name = modifier.name
        edges_op.socket_name = "Distribution Mode" 
        edges_op.socket_value = "1"
        
        # Faces button
        faces_op = row.operator("clonerpro.set_socket_value", text="Faces", depress=(mode_val == 2))
        faces_op.object_name = modifier.id_data.name
        faces_op.modifier_name = modifier.name
        faces_op.socket_name = "Distribution Mode"
        faces_op.socket_value = "2"
        
        # Face Center Mode только для Faces
        if mode_val == 2:
            col.separator()
            display_socket_prop(col, modifier, "Face Center Mode", text="One per Face (Centers)")
        
        # Count/Density параметры
        if mode_val == 1:  # Edges
            col.separator()
            display_socket_prop(col, modifier, "Instance Count", text="Count")
        elif mode_val == 2:  # Faces
            face_center = UnifiedClonerUI.get_socket_value(modifier, "Face Center Mode")
            if not face_center:
                col.separator()
                display_socket_prop(col, modifier, "Density", text="Density")
        
        # Offset & Alignment
        box = layout.box()
        col = box.column(align=True)
        col.label(text="Offset & Alignment:", icon='ORIENTATION_NORMAL')
        
        display_socket_prop(col, modifier, "Offset", text="Distance from Surface")
        display_socket_prop(col, modifier, "Align to Normal", text="Align to Surface Normals")
        
        # Instance Transform (аналогично другим)
        UnifiedClonerUI.draw_common_instance_transform(layout, modifier)
        
        # Random Transform (аналогично другим)
        UnifiedClonerUI.draw_common_random_transform(layout, modifier)
    
    @staticmethod
    def draw_spline_cloner_settings(layout, modifier):
        """Настройки Spline Cloner в стиле backup - только модификаторные сокеты"""
        # Используем fallback UI как основной (он точно соответствует backup)
        UnifiedClonerUI._draw_spline_cloner_fallback(layout, modifier)
    
    @staticmethod
    def _draw_spline_cloner_fallback(layout, modifier):
        """Fallback UI для Spline Cloner - точно как в backup"""
        from ..utils.ui_helpers import display_socket_prop
        
        # Source Mode - определяем динамически
        source_mode = UnifiedClonerUI.get_socket_value(modifier, "Source Mode")
        if source_mode is None:
            source_mode = 0
        
        # === SOURCE SETTINGS ===
        box = layout.box()
        col = box.column(align=True)
        col.label(text="Source Settings:", icon='CURVE_DATA')
        
        if source_mode == 0:  # Object mode
            display_socket_prop(col, modifier, "Source Object", text="Source Object")
        else:  # Collection mode
            display_socket_prop(col, modifier, "Source Collection", text="Source Collection")
        
        # Hide Original
        col.separator()
        display_socket_prop(col, modifier, "Hide Original", text="Hide Original Spline")
        
        # === SPLINE DISTRIBUTION ===
        box = layout.box()
        col = box.column(align=True)
        col.label(text="Spline Distribution:", icon='CURVE_PATH')
        
        col.separator()
        display_socket_prop(col, modifier, "Instance Count", text="Count")
        
        # Spacing Mode как кнопки (как в backup)
        spacing_mode = UnifiedClonerUI.get_socket_value(modifier, "Spacing Mode") 
        if spacing_mode is None:
            spacing_mode = 0
            
        row = col.row(align=True)
        row.label(text="Spacing:")
        spacing_row = col.row(align=True)
        
        # Count button
        count_op = spacing_row.operator("clonerpro.set_socket_value", text="Count", depress=(spacing_mode == 0))
        count_op.object_name = modifier.id_data.name
        count_op.modifier_name = modifier.name
        count_op.socket_name = "Spacing Mode"
        count_op.socket_value = "0"
        
        # Length button
        length_op = spacing_row.operator("clonerpro.set_socket_value", text="Length", depress=(spacing_mode == 1))
        length_op.object_name = modifier.id_data.name
        length_op.modifier_name = modifier.name
        length_op.socket_name = "Spacing Mode"
        length_op.socket_value = "1"
        
        # Показываем Spacing Length только если Spacing Mode = Length
        if spacing_mode == 1:  # Length mode
            display_socket_prop(col, modifier, "Spacing Length", text="Length")
        
        col.separator()
        display_socket_prop(col, modifier, "Curve Start", text="Start")
        display_socket_prop(col, modifier, "Curve End", text="End")
        display_socket_prop(col, modifier, "Curve Offset", text="Offset")
        display_socket_prop(col, modifier, "Align to Spline", text="Align to Spline")
        
        # === INSTANCE TRANSFORM ===
        box = layout.box()
        col = box.column(align=True)
        col.label(text="Instance Transform:", icon='OBJECT_DATA')
        
        display_socket_prop(col, modifier, "Uniform Scale", text="Uniform Scale")
        col.separator()
        display_socket_prop(col, modifier, "Instance Scale", text="Scale")
        display_socket_prop(col, modifier, "Instance Rotation", text="Rotation")
        
        # === GRADIENT SETTINGS ===
        box = layout.box()
        col = box.column(align=True)
        col.label(text="Gradient:", icon='IPO_EASE_IN_OUT')
        
        col.separator()
        col.label(text="Scale:")
        display_socket_prop(col, modifier, "Scale Start", text="Start")
        display_socket_prop(col, modifier, "Scale End", text="End")
        
        col.separator()
        col.label(text="Rotation:")
        display_socket_prop(col, modifier, "Rotation Start", text="Start")
        display_socket_prop(col, modifier, "Rotation End", text="End")
        
        # === RANDOM TRANSFORM ===
        UnifiedClonerUI.draw_common_random_transform(layout, modifier)
        
        # === COLLECTION SETTINGS ===  (только для Collection режима)
        if source_mode == 1:  # Collection mode
            box = layout.box()
            col = box.column(align=True)
            col.label(text="Collection Settings:", icon='OUTLINER_COLLECTION')
            
            col.separator()
            display_socket_prop(col, modifier, "Collection Pick Instance", text="Random Collection")
            
            # Collection Instance Index или Random Seed в зависимости от Pick Instance
            random_pick = UnifiedClonerUI.get_socket_value(modifier, "Collection Pick Instance")
            if random_pick:
                display_socket_prop(col, modifier, "Collection Random Seed", text="Collection Seed")
            else:
                display_socket_prop(col, modifier, "Collection Instance Index", text="Instance Index")
                display_socket_prop(col, modifier, "Collection Object Count", text="Object Count")
    
    @staticmethod
    def draw_common_instance_transform(layout, modifier):
        """Общая секция Instance Transform для всех клонеров"""
        box = layout.box()
        col = box.column(align=True)
        col.label(text="Instance Transform:", icon='OBJECT_DATA')
        
        # Uniform Scale checkbox (если есть)
        if UnifiedClonerUI.get_socket_value(modifier, "Uniform Scale") is not None:
            col.separator()
            display_socket_prop(col, modifier, "Uniform Scale", text="Uniform Scale")
        
        # Instance Scale
        display_socket_prop(col, modifier, "Instance Scale", text="Scale")
        
        # Instance Rotation
        col.separator()
        display_socket_prop(col, modifier, "Instance Rotation", text="Rotation")
    
    @staticmethod
    def draw_common_random_transform(layout, modifier):
        """Общая секция Random Transform для всех клонеров"""
        box = layout.box()
        col = box.column(align=True)
        col.label(text="Random Transform:", icon='FORCE_TURBULENCE')
        
        col.separator()
        display_socket_prop(col, modifier, "Random Position", text="Random Position")
        
        col.separator()
        display_socket_prop(col, modifier, "Random Rotation", text="Random Rotation")
        
        col.separator()
        display_socket_prop(col, modifier, "Random Scale", text="Random Scale")
        
        col.separator()
        display_socket_prop(col, modifier, "Random Seed", text="Random Seed")
    
    @staticmethod
    def draw_cloner_from_config(layout, modifier, cloner_type):
        """Универсальный метод отрисовки клонера из конфигурационного файла в стиле ObjectCloner"""
        
        if cloner_type == "GRID":
            UnifiedClonerUI.draw_grid_cloner_settings(layout, modifier)

        elif cloner_type == "LINEAR":
            UnifiedClonerUI.draw_linear_cloner_settings(layout, modifier)

        elif cloner_type == "CIRCLE":
            UnifiedClonerUI.draw_circle_cloner_settings(layout, modifier)

        elif cloner_type == "SPIRAL":
            UnifiedClonerUI.draw_spiral_cloner_settings(layout, modifier)
    
    @staticmethod
    def render_parameters_as_boxes(layout, modifier, parameters):
        """Отображает параметры в виде боксов ObjectCloner стиля"""
        from ..utils.ui_helpers import display_socket_prop
        
        for group_name, group_params in parameters.items():
            if group_params:
                # Создаем бокс для группы
                box = layout.box()
                col = box.column(align=True)
                
                # Заголовок группы с иконкой
                icon = UnifiedClonerUI.get_group_icon(group_name)
                col.label(text=f"{group_name}:", icon=icon)
                
                # Добавляем параметры
                col.separator()
                for param in group_params:
                    socket_name = param["socket_name"]
                    display_name = param["name"]
                    display_socket_prop(col, modifier, socket_name, text=display_name)
    
    @staticmethod
    def render_ui_groups_as_boxes(layout, modifier, ui_groups):
        """Отображает UI группы в виде боксов ObjectCloner стиля"""
        from ..utils.ui_helpers import display_socket_prop
        
        for group_info in ui_groups:
            group_name = group_info["name"]
            icon = group_info.get("icon", "SETTINGS")
            parameters = group_info["parameters"]
            
            # Создаем бокс для группы
            box = layout.box()
            col = box.column(align=True)
            
            # Заголовок группы с иконкой
            col.label(text=f"{group_name}:", icon=icon)
            
            # Добавляем параметры
            col.separator()
            for param in parameters:
                display_socket_prop(col, modifier, param, text=param)
    
    @staticmethod
    def get_group_icon(group_name):
        """Получить иконку для группы параметров"""
        icon_map = {
            "Grid Settings": "MESH_GRID",
            "Instance Transform": "OBJECT_DATA", 
            "Random Transform": "FORCE_TURBULENCE",
            "Global Transform": "WORLD_DATA",
            "Circle Settings": "MESH_CIRCLE",
            "Random Settings": "FORCE_TURBULENCE",
            "Linear Settings": "CURVE_PATH",
            "Gradient Settings": "IPO_LINEAR",
            "Transform": "OBJECT_ORIGIN",
            "Randomization": "FORCE_TURBULENCE"
        }
        return icon_map.get(group_name, "SETTINGS")
    
    @staticmethod
    def get_socket_value(modifier, socket_name):
        """Получить значение сокета"""
        from ..utils.ui_helpers import get_socket_value
        return get_socket_value(modifier, socket_name)
    


# UI Generator модуль - регистрация не требуется