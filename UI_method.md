# Справочник по UI элементам для аддонов Blender

Данный справочник содержит описание создания пользовательских интерфейсов (UI) в аддонах Blender с использованием Python API согласно ОФИЦИАЛЬНОЙ ДОКУМЕНТАЦИИ ПО API BLENDER которая расположена здесь: https://docs.blender.org/api/current/index.html

Основная документация по UI: https://docs.blender.org/api/current/bpy.types.UILayout.html

Документация по операторам: https://docs.blender.org/api/current/bpy.types.Operator.html

## Основные принципы UI в Blender

### 📋 БАЗОВАЯ СТРУКТУРА UI

---

**UILayout**
- **Основной класс для создания интерфейсов**
- **Методы размещения:**
  - `layout.row()` - Горизонтальное размещение
  - `layout.column()` - Вертикальное размещение
  - `layout.split()` - Разделение на секции
  - `layout.box()` - Группировка в рамку
  - `layout.separator()` - Разделитель

```python
def draw(self, context):
    layout = self.layout
    
    # Горизонтальная строка
    row = layout.row()
    row.prop(obj, "location")
    
    # Вертикальная колонка
    col = layout.column()
    col.prop(obj, "rotation_euler")
    
    # Группировка в рамку
    box = layout.box()
    box.label(text="Настройки")
```

---

### 🎛️ ОСНОВНЫЕ UI ЭЛЕМЕНТЫ

---

**Кнопки (Buttons)**
```python
# Простая кнопка оператора
layout.operator("mesh.primitive_cube_add", text="Добавить куб")

# Кнопка с иконкой
layout.operator("mesh.primitive_cube_add", text="Куб", icon='MESH_CUBE')

# Кнопка без текста (только иконка)
layout.operator("mesh.select_all", text="", icon='SELECT_ALL')

# Большая кнопка
row = layout.row()
row.scale_y = 2.0
row.operator("render.render", text="РЕНДЕР", icon='RENDER_STILL')
```

**Свойства (Properties)**
```python
# Числовое поле
layout.prop(obj, "location")

# Поле с кастомным названием
layout.prop(obj, "name", text="Имя объекта")

# Слайдер
layout.prop(mat, "metallic", slider=True)

# Поле без названия
row = layout.row()
row.prop(obj, "location", text="")

# Процентное значение
layout.prop(mat, "roughness", text="Шероховатость %")
```

**Чекбоксы (Checkboxes)**
```python
# Простой чекбокс
layout.prop(obj, "show_in_front", text="Показывать поверх")

# Переключатель (toggle)
layout.prop(scene, "use_nodes", toggle=True)

# Иконка-переключатель
layout.prop(obj, "hide_viewport", text="", icon='HIDE_OFF', toggle=True)
```

**Выпадающие списки (Enums)**
```python
# Стандартный выпадающий список
layout.prop(obj, "type")

# Развернутый список опций
layout.prop(obj, "type", expand=True)

# Список с иконками
layout.prop_enum(scene.render.engine, expand=True)
```

**Поля ввода (Text Fields)**
```python
# Текстовое поле
layout.prop(obj, "name")

# Многострочное поле
layout.prop(text, "body", text="")

# Поле пароля
layout.prop(addon_prefs, "api_key", text="API ключ")
```

---

### 📐 LAYOUT СИСТЕМЫ

---

**Row Layout (Горизонтальное размещение)**
```python
def draw(self, context):
    layout = self.layout
    
    # Простая строка
    row = layout.row()
    row.prop(obj, "location")
    
    # Строка с выравниванием
    row = layout.row(align=True)
    row.prop(obj, "scale", text="")
    
    # Строка с одинаковыми пропорциями
    row = layout.row(align=True)
    row.operator("transform.translate")
    row.operator("transform.rotate")
    row.operator("transform.resize")
```

**Column Layout (Вертикальное размещение)**
```python
def draw(self, context):
    layout = self.layout
    
    # Простая колонка
    col = layout.column()
    col.prop(obj, "location")
    col.prop(obj, "rotation_euler")
    col.prop(obj, "scale")
    
    # Выровненная колонка
    col = layout.column(align=True)
    col.prop(obj, "location")
    
    # Субколонка
    sub = col.column()
    sub.enabled = obj.show_in_front
    sub.prop(obj, "show_transparent")
```

**Split Layout (Разделение на секции)**
```python
def draw(self, context):
    layout = self.layout
    
    # Разделение 50/50
    split = layout.split()
    split.label(text="Левая часть")
    split.label(text="Правая часть")
    
    # Разделение с кастомными пропорциями
    split = layout.split(factor=0.3)
    split.label(text="30%")
    split.label(text="70%")
    
    # Вложенные разделения
    split = layout.split()
    col = split.column()
    col.prop(obj, "location")
    col = split.column()
    col.prop(obj, "rotation_euler")
```

**Box Layout (Группировка в рамки)**
```python
def draw(self, context):
    layout = self.layout
    
    # Простая рамка
    box = layout.box()
    box.label(text="Настройки трансформации")
    box.prop(obj, "location")
    box.prop(obj, "rotation_euler")
    
    # Вложенные рамки
    box = layout.box()
    box.label(text="Материалы")
    
    if obj.material_slots:
        for i, slot in enumerate(obj.material_slots):
            sub_box = box.box()
            sub_box.prop(slot, "material", text=f"Слот {i+1}")
```

---

### 🎯 СПЕЦИАЛЬНЫЕ UI ЭЛЕМЕНТЫ

---

**Labels (Текстовые метки)**
```python
# Простая метка
layout.label(text="Информация")

# Метка с иконкой
layout.label(text="Предупреждение", icon='ERROR')

# Метка с переменным текстом
layout.label(text=f"Объектов выбрано: {len(context.selected_objects)}")

# Центрированная метка
row = layout.row()
row.alignment = 'CENTER'
row.label(text="Центральный текст")
```

**Separators (Разделители)**
```python
# Горизонтальный разделитель
layout.separator()

# Разделитель с отступом
layout.separator(factor=2.0)

# Разделительная линия
layout.separator_spacer()
```

**Progress Bars (Индикаторы прогресса)**
```python
# Прогресс-бар (только для чтения)
layout.progress(factor=0.7, text="Прогресс: 70%")

# Через template
layout.template_running_jobs()
```

**Color Pickers (Выбор цвета)**
```python
# Цветовая панель
layout.prop(mat, "diffuse_color", text="Цвет")

# Компактный выбор цвета
row = layout.row()
row.prop(mat, "diffuse_color", text="")

# Расширенный выбор цвета
layout.template_color_picker(mat, "diffuse_color")
```

---

### 📊 СПИСКИ И КОЛЛЕКЦИИ

---

**UIList (Списки элементов)**
```python
class MESH_UL_material_list(bpy.types.UIList):
    def draw_item(self, context, layout, data, item, icon, 
                  active_data, active_propname, index):
        if self.layout_type in {'DEFAULT', 'COMPACT'}:
            layout.prop(item, "name", text="", emboss=False, icon='MATERIAL')
        elif self.layout_type in {'GRID'}:
            layout.alignment = 'CENTER'
            layout.label(text="", icon='MATERIAL')

# Использование в draw методе
def draw(self, context):
    layout = self.layout
    obj = context.object
    
    row = layout.row()
    row.template_list("MESH_UL_material_list", "", obj, "material_slots", 
                      obj, "active_material_index")
    
    col = row.column(align=True)
    col.operator("object.material_slot_add", icon='ADD', text="")
    col.operator("object.material_slot_remove", icon='REMOVE', text="")
```

**Collection Properties**
```python
# Определение коллекции
class MyPropertyGroup(bpy.types.PropertyGroup):
    name: bpy.props.StringProperty(name="Имя")
    value: bpy.props.FloatProperty(name="Значение")

# Регистрация
bpy.utils.register_class(MyPropertyGroup)
bpy.types.Scene.my_collection = bpy.props.CollectionProperty(type=MyPropertyGroup)
bpy.types.Scene.my_index = bpy.props.IntProperty()

# Отображение в UI
def draw(self, context):
    layout = self.layout
    scene = context.scene
    
    row = layout.row()
    row.template_list("UI_UL_list", "", scene, "my_collection", 
                      scene, "my_index")
```

---

### 🎨 ПАНЕЛИ И МЕНЮ

---

**Базовая панель**
```python
class VIEW3D_PT_my_panel(bpy.types.Panel):
    bl_label = "Моя панель"
    bl_idname = "VIEW3D_PT_my_panel"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = "Tool"  # Вкладка в боковой панели
    bl_context = "objectmode"  # Контекст показа
    
    @classmethod
    def poll(cls, context):
        # Условие показа панели
        return context.object is not None
    
    def draw_header(self, context):
        # Заголовок панели
        self.layout.label(text="", icon='MODIFIER')
    
    def draw(self, context):
        layout = self.layout
        obj = context.object
        
        layout.prop(obj, "name")
        layout.prop(obj, "location")
        
        # Подпанель
        layout.label(text="Дополнительно:")
        box = layout.box()
        box.prop(obj, "show_in_front")
```

**Выпадающие меню**
```python
class VIEW3D_MT_my_menu(bpy.types.Menu):
    bl_label = "Мое меню"
    bl_idname = "VIEW3D_MT_my_menu"
    
    def draw(self, context):
        layout = self.layout
        
        # Операторы
        layout.operator("mesh.primitive_cube_add", text="Куб")
        layout.operator("mesh.primitive_sphere_add", text="Сфера")
        
        # Разделитель
        layout.separator()
        
        # Подменю
        layout.menu("VIEW3D_MT_add", text="Добавить")
        
        # Условный элемент
        if context.object:
            layout.operator("object.delete", text="Удалить")

# Добавление в существующее меню
def menu_func(self, context):
    self.layout.menu("VIEW3D_MT_my_menu")

bpy.types.VIEW3D_MT_add.append(menu_func)
```

**Контекстные меню**
```python
class VIEW3D_MT_context_menu(bpy.types.Menu):
    bl_label = "Контекстное меню"
    
    def draw(self, context):
        layout = self.layout
        
        layout.operator_context = 'INVOKE_REGION_WIN'
        
        layout.operator("transform.translate")
        layout.operator("transform.rotate")
        layout.operator("transform.resize")
        
        layout.separator()
        
        layout.operator("object.duplicate_move")
        layout.operator("object.delete")

# Регистрация контекстного меню
def register():
    bpy.types.VIEW3D_MT_object_context_menu.prepend(menu_draw)
```

---

### 🔧 ОПЕРАТОРЫ И ДИАЛОГИ

---

**Простой оператор**
```python
class MESH_OT_my_operator(bpy.types.Operator):
    bl_idname = "mesh.my_operator"
    bl_label = "Мой оператор"
    bl_description = "Описание того, что делает оператор"
    bl_options = {'REGISTER', 'UNDO'}
    
    # Свойства оператора
    my_prop: bpy.props.FloatProperty(
        name="Мое свойство",
        description="Описание свойства",
        default=1.0,
        min=0.0,
        max=10.0
    )
    
    @classmethod
    def poll(cls, context):
        return context.object is not None
    
    def execute(self, context):
        # Основная логика
        obj = context.object
        obj.location.z += self.my_prop
        return {'FINISHED'}
    
    def invoke(self, context, event):
        # Вызов с диалогом
        return context.window_manager.invoke_props_dialog(self)
    
    def draw(self, context):
        # UI для диалога
        layout = self.layout
        layout.prop(self, "my_prop")
```

**Модальный оператор**
```python
class VIEW3D_OT_modal_operator(bpy.types.Operator):
    bl_idname = "view3d.modal_operator"
    bl_label = "Модальный оператор"
    
    def __init__(self):
        self.initial_mouse = (0, 0)
        self.initial_value = 0.0
    
    def modal(self, context, event):
        if event.type == 'MOUSEMOVE':
            # Обработка движения мыши
            delta = event.mouse_x - self.initial_mouse[0]
            context.object.location.x = self.initial_value + delta * 0.01
            
        elif event.type == 'LEFTMOUSE':
            return {'FINISHED'}
            
        elif event.type in {'RIGHTMOUSE', 'ESC'}:
            # Отмена
            context.object.location.x = self.initial_value
            return {'CANCELLED'}
        
        return {'RUNNING_MODAL'}
    
    def invoke(self, context, event):
        if context.object:
            self.initial_mouse = (event.mouse_x, event.mouse_y)
            self.initial_value = context.object.location.x
            context.window_manager.modal_handler_add(self)
            return {'RUNNING_MODAL'}
        else:
            self.report({'WARNING'}, "Нет активного объекта")
            return {'CANCELLED'}
```

**Файловый браузер**
```python
class IMPORT_OT_my_format(bpy.types.Operator):
    bl_idname = "import_scene.my_format"
    bl_label = "Импорт моего формата"
    bl_options = {'REGISTER', 'UNDO'}
    
    # Свойства файлового браузера
    filepath: bpy.props.StringProperty(
        name="Файл",
        description="Путь к файлу",
        maxlen=1024,
        subtype='FILE_PATH'
    )
    
    filter_glob: bpy.props.StringProperty(
        default="*.myext",
        options={'HIDDEN'}
    )
    
    def execute(self, context):
        # Логика импорта
        print(f"Импорт файла: {self.filepath}")
        return {'FINISHED'}
    
    def invoke(self, context, event):
        context.window_manager.fileselect_add(self)
        return {'RUNNING_MODAL'}
```

---

### 🎪 ШАБЛОНЫ И ТЕМПЛЕЙТЫ

---

**ID Template**
```python
# Выбор объекта/материала/текстуры
layout.template_ID(context.object, "active_material", new="material.new")

# С предварительным просмотром
layout.template_ID_preview(context.object, "active_material")

# Без кнопки создания
layout.template_ID(context.object, "active_material", new="")
```

**Preview Template**
```python
# Предварительный просмотр материала
layout.template_preview(context.object.active_material)

# Иконка предварительного просмотра
layout.template_icon(icon_value=icon, scale=5.0)
```

**Node Template**
```python
# Дерево нод
layout.template_node_view(material, "node_tree", material.node_tree.nodes.active)

# Список нод
layout.template_node_inputs(node)
```

**Curve Template**
```python
# Кривая для анимации
layout.template_curve_mapping(modifier, "curve")

# Цветовая рампа
layout.template_color_ramp(material, "color_ramp")
```

---

### 🔍 ПОИСК И ФИЛЬТРАЦИЯ

---

**Search Popup**
```python
class WM_OT_search_menu(bpy.types.Operator):
    bl_idname = "wm.search_menu"
    bl_label = "Поиск"
    bl_property = "my_enum"
    
    my_enum: bpy.props.EnumProperty(
        items=[
            ('CUBE', "Куб", "Добавить куб"),
            ('SPHERE', "Сфера", "Добавить сферу"),
            ('CYLINDER', "Цилиндр", "Добавить цилиндр"),
        ]
    )
    
    def execute(self, context):
        print(f"Выбрано: {self.my_enum}")
        return {'FINISHED'}
    
    def invoke(self, context, event):
        context.window_manager.invoke_search_popup(self)
        return {'FINISHED'}

# Использование
layout.operator("wm.search_menu", text="Поиск объектов", icon='VIEWZOOM')
```

**Фильтрация списков**
```python
class OBJECT_UL_filtered_list(bpy.types.UIList):
    def draw_item(self, context, layout, data, item, icon, 
                  active_data, active_propname, index):
        layout.prop(item, "name", text="", emboss=False)
    
    def filter_items(self, context, data, propname):
        objects = getattr(data, propname)
        filter_flags = []
        order_flags = []
        
        # Фильтрация по имени
        if self.filter_name:
            filter_flags = bpy.types.UI_UL_list.filter_items_by_name(
                self.filter_name, self.bitflag_filter_item, objects, "name"
            )
        
        if not filter_flags:
            filter_flags = [self.bitflag_filter_item] * len(objects)
        
        # Сортировка
        if self.use_filter_sort_alpha:
            order_flags = bpy.types.UI_UL_list.sort_items_by_name(objects, "name")
        
        return filter_flags, order_flags
```

---

### 💡 УВЕДОМЛЕНИЯ И ОБРАТНАЯ СВЯЗЬ

---

**Report Messages**
```python
class MY_OT_example(bpy.types.Operator):
    bl_idname = "my.example"
    bl_label = "Пример"
    
    def execute(self, context):
        # Различные типы сообщений
        self.report({'INFO'}, "Операция завершена успешно")
        self.report({'WARNING'}, "Предупреждение")
        self.report({'ERROR'}, "Произошла ошибка")
        
        return {'FINISHED'}
```

**Status Bar**
```python
def execute(self, context):
    # Обновление статусной строки
    context.workspace.status_text_set("Обработка...")
    
    # Логика операции
    # ...
    
    # Очистка статуса
    context.workspace.status_text_set(None)
    
    return {'FINISHED'}
```

**Progress Updates**
```python
def execute(self, context):
    total = 100
    for i in range(total):
        # Обновление прогресса
        progress = i / total
        self.report({'INFO'}, f"Прогресс: {progress*100:.1f}%")
        
        # Принудительное обновление UI
        bpy.context.window_manager.progress_update(progress)
        
        # Проверка на отмену
        if bpy.context.window_manager.progress_get_is_cancelled():
            return {'CANCELLED'}
    
    bpy.context.window_manager.progress_end()
    return {'FINISHED'}
```

---

### 📱 АДАПТИВНЫЙ И ОТЗЫВЧИВЫЙ UI

---

**Условное отображение**
```python
def draw(self, context):
    layout = self.layout
    obj = context.object
    
    # Условие для показа элементов
    if obj and obj.type == 'MESH':
        layout.prop(obj, "show_in_front")
        
        # Вложенные условия
        if obj.mode == 'EDIT':
            layout.operator("mesh.select_all")
        else:
            layout.operator("object.mode_set", text="Режим редактирования").mode = 'EDIT'
    
    # Активация/деактивация элементов
    row = layout.row()
    row.enabled = obj is not None
    row.prop(context.scene, "frame_current")
```

**Масштабирование элементов**
```python
def draw(self, context):
    layout = self.layout
    
    # Масштабирование по Y
    row = layout.row()
    row.scale_y = 1.5
    row.operator("render.render", text="РЕНДЕР")
    
    # Масштабирование по X
    col = layout.column()
    col.scale_x = 0.8
    col.prop(context.object, "location")
    
    # Общее масштабирование
    box = layout.box()
    box.scale_x = 1.2
    box.scale_y = 0.9
```

**Выравнивание**
```python
def draw(self, context):
    layout = self.layout
    
    # Выравнивание по центру
    row = layout.row()
    row.alignment = 'CENTER'
    row.label(text="Центрированный текст")
    
    # Выравнивание справа
    row = layout.row()
    row.alignment = 'RIGHT'
    row.label(text="Справа")
    
    # Расширение на всю ширину
    row = layout.row()
    row.alignment = 'EXPAND'
    row.operator("mesh.primitive_cube_add")
```

---

### 🎨 КАСТОМИЗАЦИЯ И СТИЛИЗАЦИЯ

---

**Активные/неактивные состояния**
```python
def draw(self, context):
    layout = self.layout
    obj = context.object
    
    # Деактивация элемента
    row = layout.row()
    row.enabled = obj is not None
    row.prop(context.scene, "frame_current")
    
    # Предупреждающий стиль
    row = layout.row()
    row.alert = True  # Красная подсветка
    row.prop(obj, "name")
    
    # Активная кнопка
    row = layout.row()
    row.active = obj.select_get()
    row.operator("object.delete")
```

**Эмбосс и стили**
```python
def draw(self, context):
    layout = self.layout
    
    # Без рамки (плоский стиль)
    row = layout.row()
    row.prop(obj, "name", text="", emboss=False)
    
    # Принудительная рамка
    row = layout.row()
    row.prop(obj, "location", emboss=True)
    
    # Стиль кнопки
    layout.operator_context = 'INVOKE_DEFAULT'
    layout.operator("mesh.primitive_cube_add", emboss=False)
```

---

### 🔗 СВЯЗЫВАНИЕ С АНИМАТОРОМ

---

**Keyframe UI**
```python
def draw(self, context):
    layout = self.layout
    obj = context.object
    
    # Кнопка ключевого кадра
    layout.prop(obj, "location", icon='KEYFRAME')
    
    # Вставка ключевого кадра
    row = layout.row(align=True)
    row.prop(obj, "location")
    row.operator("anim.keyframe_insert_menu", text="", icon='KEY_HLT')
    
    # Состояние анимации
    if obj.animation_data and obj.animation_data.action:
        layout.label(text="Анимирован", icon='ANIM')
```

**Timeline Integration**
```python
def draw(self, context):
    layout = self.layout
    scene = context.scene
    
    # Контролы воспроизведения
    row = layout.row(align=True)
    row.operator("screen.animation_play", text="", icon='PLAY')
    row.operator("screen.animation_cancel", text="", icon='PAUSE')
    
    # Текущий кадр
    layout.prop(scene, "frame_current", text="Кадр")
    
    # Диапазон анимации
    row = layout.row(align=True)
    row.prop(scene, "frame_start", text="Начало")
    row.prop(scene, "frame_end", text="Конец")
```

---

## 🚀 Практические советы и рекомендации

### Производительность UI
1. **Ленивое обновление**: Используйте `poll()` методы для условного показа панелей
2. **Кэширование**: Избегайте тяжелых вычислений в `draw()` методах
3. **Условная отрисовка**: Используйте `if` условия для показа только нужных элементов
4. **Группировка**: Объединяйте связанные элементы в рамки и группы

### UX/UI рекомендации
1. **Консистентность**: Следуйте стандартам Blender UI
2. **Иконки**: Используйте встроенные иконки Blender для знакомости
3. **Группировка**: Логически группируйте элементы
4. **Обратная связь**: Всегда предоставляйте feedback пользователю

### Отладка UI
1. **Poll методы**: Проверяйте условия показа панелей
2. **Exception handling**: Обрабатывайте исключения в draw методах
3. **Консоль**: Используйте `print()` для отладки значений
4. **Reload addon**: Перезагружайте аддон для тестирования изменений

### Типичные паттерны
1. **Панель настроек**: Header + Properties + Operators
2. **Список с кнопками**: UIList + Add/Remove buttons
3. **Модальная операция**: Invoke → Modal → Draw
4. **Файловый импорт**: Invoke Fileselect → Execute

### Интеграция с Blender API
1. **Context dependency**: Всегда проверяйте доступность context объектов
2. **Property updates**: Используйте `update` callbacks для синхронизации
3. **Undo system**: Регистрируйте операторы с `UNDO` опцией
4. **Memory management**: Правильно регистрируйте/разрегистрируйте классы

### Локализация
1. **Text strings**: Используйте отдельные строки для перевода
2. **Icon consistency**: Иконки универсальны для всех языков
3. **Layout flexibility**: Учитывайте разную длину переводов

### Тестирование
1. **Разные разрешения**: Тестируйте на разных размерах экрана
2. **Разные темы**: Проверяйте в светлой и темной темах
3. **Keyboard shortcuts**: Обеспечьте поддержку горячих клавиш
4. **Error handling**: Тестируйте граничные случаи

---

## 📚 Полезные ресурсы

### Официальная документация
- **API Documentation**: https://docs.blender.org/api/current/
- **UI Layout**: https://docs.blender.org/api/current/bpy.types.UILayout.html
- **Operators**: https://docs.blender.org/api/current/bpy.types.Operator.html
- **Panels**: https://docs.blender.org/api/current/bpy.types.Panel.html

### Примеры кода
- **Template Scripts**: В Blender Editor → Templates
- **Built-in Addons**: Изучайте исходники встроенных аддонов
- **Community**: https://blender.stackexchange.com/

### Инструменты разработки
- **Console**: Используйте Python консоль в Blender
- **Text Editor**: Встроенный редактор с подсветкой синтаксиса
- **Developer Extras**: Включите в настройках для дополнительных инструментов

---