"""
Spiral Cloner для ClonerPro
Реализует спиральное распределение объектов в 3D пространстве
"""

import bpy
import bmesh
import mathutils
from mathutils import Vector
from ..base_cloner import BaseCloner


class SpiralCloner(BaseCloner):
    """
    Spiral Cloner для создания спиральных распределений
    
    Создает спиральные паттерны с настраиваемыми параметрами:
    - Count: количество экземпляров
    - Turns: количество витков спирали
    - Height: высота спирали
    - Radius: радиус спирали
    - Start Radius: начальный радиус (для конических спиралей)
    - End Radius: конечный радиус (для конических спиралей)
    """
    
    bl_idname = "SPIRAL"
    
    def __init__(self):
        super().__init__()
    
    def get_specific_sockets(self):
        """
        Специфичные сокеты для Spiral Cloner

        Returns:
            list: Список специфичных сокетов (name, socket_type, in_out, default)
        """
        return [
            ("Count", "NodeSocketInt", "INPUT", 20),
            ("Turns", "NodeSocketFloat", "INPUT", 3.0),
            ("Height", "NodeSocketFloat", "INPUT", 5.0),
            ("Start Radius", "NodeSocketFloat", "INPUT", 2.0),
            ("End Radius", "NodeSocketFloat", "INPUT", 2.0),
            ("Axis", "NodeSocketVector", "INPUT", (0.0, 0.0, 1.0)),
        ]
    
    
    def _create_cloner_logic(self, base_nodes, mode):
        """
        Основная логика Spiral Cloner
        
        Args:
            base_nodes: Словарь с базовыми нодами
            mode: Режим клонера
            
        Returns:
            NodeSocket: Финальный выход геометрии
        """
        nodes = base_nodes['nodes']
        links = base_nodes['links']
        group_input = base_nodes['group_input']
        
        # Получаем input геометрию в зависимости от режима
        geometry_input = self.get_geometry_input(base_nodes, mode)
        
        # Создаем спиральное распределение точек
        spiral_points = self._create_spiral_distribution(base_nodes)

        # Применяем поворот согласно Axis к точкам спирали
        rotated_spiral_points = self._apply_axis_rotation(base_nodes, spiral_points)

        # Инстансируем геометрию на точки
        instance_node = nodes.new('GeometryNodeInstanceOnPoints')
        instance_node.name = "Instance on Spiral"
        instance_node.location = (400, 0)
        links.new(rotated_spiral_points, instance_node.inputs['Points'])
        links.new(geometry_input, instance_node.inputs['Instance'])
        
        # Получаем индекс для рандомизации
        index_node = nodes.new('GeometryNodeInputIndex')
        index_node.location = (200, -200)
        
        # Применяем спиральную ориентацию (поворот по касательной к спирали)
        instances_with_spiral_rotation = self._apply_spiral_rotation(base_nodes, instance_node.outputs['Instances'])
        
        # Применяем instance трансформации
        instances_with_transforms = self.apply_instance_transforms(base_nodes, instances_with_spiral_rotation)
        
        # Применяем рандомизацию
        randomized_instances = self.apply_random_transforms(base_nodes, instances_with_transforms, index_node.outputs['Index'])
        
        # Применяем глобальные трансформации
        final_geometry = self.apply_global_transforms(base_nodes, randomized_instances)
        
        return final_geometry
    
    def _create_spiral_distribution(self, base_nodes):
        """
        Создание спирального распределения точек
        
        Args:
            base_nodes: Словарь с базовыми нодами
            
        Returns:
            NodeSocket: Выход с спиральными точками
        """
        nodes = base_nodes['nodes']
        links = base_nodes['links']
        group_input = base_nodes['group_input']
        
        # Создаем кривую спирали используя математические ноды
        # Сначала создаем линию точек для параметризации
        mesh_line = nodes.new('GeometryNodeMeshLine')
        mesh_line.name = "Spiral Parameter Line"
        mesh_line.mode = 'OFFSET'
        mesh_line.count_mode = 'TOTAL'
        mesh_line.location = (-800, 200)
        links.new(group_input.outputs['Count'], mesh_line.inputs['Count'])
        # Offset будет (1,0,0) для создания параметра t от 0 до Count-1
        mesh_line.inputs['Offset'].default_value = (1.0, 0.0, 0.0)
        
        # Преобразуем mesh в точки для получения индексов
        mesh_to_points = nodes.new('GeometryNodeMeshToPoints')
        mesh_to_points.name = "Parameter Points"
        mesh_to_points.mode = 'VERTICES'
        mesh_to_points.location = (-600, 200)
        links.new(mesh_line.outputs['Mesh'], mesh_to_points.inputs['Mesh'])
        
        # Получаем позицию точек (это даст нам параметр t)
        position_node = nodes.new('GeometryNodeInputPosition')
        position_node.location = (-400, 300)
        
        # Извлекаем X компонент как параметр t
        separate_xyz = nodes.new('ShaderNodeSeparateXYZ')
        separate_xyz.name = "Extract Parameter T"
        separate_xyz.location = (-200, 300)
        links.new(position_node.outputs['Position'], separate_xyz.inputs['Vector'])
        
        # Нормализуем параметр t от 0 до 1
        math_divide = nodes.new('ShaderNodeMath')
        math_divide.name = "Normalize T"
        math_divide.operation = 'DIVIDE'
        math_divide.location = (0, 300)
        links.new(separate_xyz.outputs['X'], math_divide.inputs[0])
        
        # Подключаем Count-1 как делитель
        math_subtract = nodes.new('ShaderNodeMath')
        math_subtract.name = "Count Minus 1"
        math_subtract.operation = 'SUBTRACT'
        math_subtract.location = (-100, 400)
        links.new(group_input.outputs['Count'], math_subtract.inputs[0])
        math_subtract.inputs[1].default_value = 1.0
        links.new(math_subtract.outputs['Value'], math_divide.inputs[1])
        
        # Создаем спиральные координаты
        spiral_coords = self._create_spiral_coordinates(base_nodes, math_divide.outputs['Value'])
        
        # Устанавливаем позицию точек
        set_position = nodes.new('GeometryNodeSetPosition')
        set_position.name = "Set Spiral Position"
        set_position.location = (600, 200)
        links.new(mesh_to_points.outputs['Points'], set_position.inputs['Geometry'])
        links.new(spiral_coords, set_position.inputs['Position'])
        
        return set_position.outputs['Geometry']
    
    def _create_spiral_coordinates(self, base_nodes, t_parameter):
        """
        Создание координат спирали по параметру t
        
        Args:
            base_nodes: Словарь с базовыми нодами
            t_parameter: NodeSocket с параметром t (0-1)
            
        Returns:
            NodeSocket: Выход с координатами спирали
        """
        nodes = base_nodes['nodes']
        links = base_nodes['links']
        group_input = base_nodes['group_input']
        
        # Вычисляем угол: angle = t * turns * 2π
        math_multiply_turns = nodes.new('ShaderNodeMath')
        math_multiply_turns.name = "T * Turns"
        math_multiply_turns.operation = 'MULTIPLY'
        math_multiply_turns.location = (200, 500)
        links.new(t_parameter, math_multiply_turns.inputs[0])
        links.new(group_input.outputs['Turns'], math_multiply_turns.inputs[1])
        
        math_multiply_2pi = nodes.new('ShaderNodeMath')
        math_multiply_2pi.name = "Angle = T * Turns * 2π"
        math_multiply_2pi.operation = 'MULTIPLY'
        math_multiply_2pi.location = (400, 500)
        math_multiply_2pi.inputs[1].default_value = 6.283185307  # 2π
        links.new(math_multiply_turns.outputs['Value'], math_multiply_2pi.inputs[0])
        
        # Интерполируем радиус между Start Radius и End Radius
        mix_radius = nodes.new('ShaderNodeMix')
        mix_radius.name = "Interpolate Radius"
        mix_radius.data_type = 'FLOAT'
        mix_radius.location = (200, 600)
        links.new(t_parameter, mix_radius.inputs['Factor'])
        links.new(group_input.outputs['Start Radius'], mix_radius.inputs['A'])
        links.new(group_input.outputs['End Radius'], mix_radius.inputs['B'])
        
        # Вычисляем X = radius * cos(angle)
        math_cos = nodes.new('ShaderNodeMath')
        math_cos.name = "Cos(Angle)"
        math_cos.operation = 'COSINE'
        math_cos.location = (600, 500)
        links.new(math_multiply_2pi.outputs['Value'], math_cos.inputs[0])
        
        math_x = nodes.new('ShaderNodeMath')
        math_x.name = "X = Radius * Cos"
        math_x.operation = 'MULTIPLY'
        math_x.location = (800, 500)
        links.new(mix_radius.outputs['Result'], math_x.inputs[0])
        links.new(math_cos.outputs['Value'], math_x.inputs[1])
        
        # Вычисляем Y = radius * sin(angle)
        math_sin = nodes.new('ShaderNodeMath')
        math_sin.name = "Sin(Angle)"
        math_sin.operation = 'SINE'
        math_sin.location = (600, 400)
        links.new(math_multiply_2pi.outputs['Value'], math_sin.inputs[0])
        
        math_y = nodes.new('ShaderNodeMath')
        math_y.name = "Y = Radius * Sin"
        math_y.operation = 'MULTIPLY'
        math_y.location = (800, 400)
        links.new(mix_radius.outputs['Result'], math_y.inputs[0])
        links.new(math_sin.outputs['Value'], math_y.inputs[1])
        
        # Вычисляем Z = t * height
        math_z = nodes.new('ShaderNodeMath')
        math_z.name = "Z = T * Height"
        math_z.operation = 'MULTIPLY'
        math_z.location = (800, 300)
        links.new(t_parameter, math_z.inputs[0])
        links.new(group_input.outputs['Height'], math_z.inputs[1])
        
        # Объединяем координаты в вектор
        combine_xyz = nodes.new('ShaderNodeCombineXYZ')
        combine_xyz.name = "Combine Spiral Coordinates"
        combine_xyz.location = (1000, 400)
        links.new(math_x.outputs['Value'], combine_xyz.inputs['X'])
        links.new(math_y.outputs['Value'], combine_xyz.inputs['Y'])
        links.new(math_z.outputs['Value'], combine_xyz.inputs['Z'])

        return combine_xyz.outputs['Vector']

    def _apply_axis_rotation(self, base_nodes, spiral_geometry):
        """
        Применение поворота спирали согласно заданной оси

        Args:
            base_nodes: Словарь с базовыми нодами
            spiral_geometry: NodeSocket с геометрией спирали

        Returns:
            NodeSocket: Повернутая геометрия спирали
        """
        nodes = base_nodes['nodes']
        links = base_nodes['links']
        group_input = base_nodes['group_input']

        # Вычисляем поворот от оси Z (0,0,1) к заданной оси
        default_axis = nodes.new('ShaderNodeCombineXYZ')
        default_axis.name = "Default Z Axis"
        default_axis.location = (300, 600)
        default_axis.inputs['X'].default_value = 0.0
        default_axis.inputs['Y'].default_value = 0.0
        default_axis.inputs['Z'].default_value = 1.0

        # Нормализуем входную ось
        normalize_axis = nodes.new('ShaderNodeVectorMath')
        normalize_axis.name = "Normalize Target Axis"
        normalize_axis.operation = 'NORMALIZE'
        normalize_axis.location = (300, 400)
        links.new(group_input.outputs['Axis'], normalize_axis.inputs[0])

        # Вычисляем cross product для оси поворота
        cross_product = nodes.new('ShaderNodeVectorMath')
        cross_product.name = "Cross Product"
        cross_product.operation = 'CROSS_PRODUCT'
        cross_product.location = (500, 500)
        links.new(default_axis.outputs['Vector'], cross_product.inputs[0])
        links.new(normalize_axis.outputs['Vector'], cross_product.inputs[1])

        # Вычисляем dot product для угла поворота
        dot_product = nodes.new('ShaderNodeVectorMath')
        dot_product.name = "Dot Product"
        dot_product.operation = 'DOT_PRODUCT'
        dot_product.location = (500, 300)
        links.new(default_axis.outputs['Vector'], dot_product.inputs[0])
        links.new(normalize_axis.outputs['Vector'], dot_product.inputs[1])

        # Вычисляем угол через arccos
        arccos_angle = nodes.new('ShaderNodeMath')
        arccos_angle.name = "Arccos Angle"
        arccos_angle.operation = 'ARCCOSINE'
        arccos_angle.location = (700, 300)
        links.new(dot_product.outputs['Value'], arccos_angle.inputs[0])

        # Применяем поворот к геометрии
        transform_geometry = nodes.new('GeometryNodeTransform')
        transform_geometry.name = "Rotate Spiral Geometry"
        transform_geometry.location = (900, 200)
        links.new(spiral_geometry, transform_geometry.inputs['Geometry'])

        # Создаем вектор поворота (ось * угол)
        multiply_axis_angle = nodes.new('ShaderNodeVectorMath')
        multiply_axis_angle.name = "Axis * Angle"
        multiply_axis_angle.operation = 'SCALE'
        multiply_axis_angle.location = (700, 500)
        links.new(cross_product.outputs['Vector'], multiply_axis_angle.inputs[0])
        links.new(arccos_angle.outputs['Value'], multiply_axis_angle.inputs[3])

        links.new(multiply_axis_angle.outputs['Vector'], transform_geometry.inputs['Rotation'])

        return transform_geometry.outputs['Geometry']

    def _apply_spiral_rotation(self, base_nodes, instances_input):
        """
        Применение поворота по касательной к спирали
        
        Args:
            base_nodes: Словарь с базовыми нодами
            instances_input: NodeSocket с инстансами
            
        Returns:
            NodeSocket: Выход с повернутыми инстансами
        """
        nodes = base_nodes['nodes']
        links = base_nodes['links']
        
        # Пока что возвращаем инстансы без дополнительного поворота
        # В будущем можно добавить вычисление касательной к спирали
        # и поворот инстансов вдоль этой касательной
        
        return instances_input
    
    def register_properties(self, props_owner):
        """
        Регистрация всех свойств Spiral Cloner
        """
        # Spiral Settings
        props_owner.spiral_count = bpy.props.IntProperty(
            name="Count",
            description="Number of instances in spiral",
            default=20,
            min=1,
            max=10000
        )

        props_owner.spiral_turns = bpy.props.FloatProperty(
            name="Turns",
            description="Number of spiral turns",
            default=3.0,
            min=0.1,
            max=100.0
        )

        props_owner.spiral_height = bpy.props.FloatProperty(
            name="Height",
            description="Height of the spiral",
            default=5.0,
            min=0.0,
            max=1000.0
        )



        props_owner.spiral_start_radius = bpy.props.FloatProperty(
            name="Start Radius",
            description="Starting radius of the spiral",
            default=2.0,
            min=0.0,
            max=1000.0
        )

        props_owner.spiral_end_radius = bpy.props.FloatProperty(
            name="End Radius",
            description="Ending radius of the spiral",
            default=2.0,
            min=0.0,
            max=1000.0
        )

        props_owner.spiral_axis = bpy.props.FloatVectorProperty(
            name="Axis",
            description="Spiral axis direction",
            default=(0.0, 0.0, 1.0),
            size=3,
            subtype='XYZ'
        )

    def draw_ui(self, layout, props_owner, modifier=None):
        """
        Отрисовка UI для Spiral Cloner

        Args:
            layout: UI layout
            props_owner: Объект с свойствами
            modifier: Модификатор (если есть)
        """
        # Spiral Settings
        box = layout.box()
        col = box.column(align=True)
        col.label(text="Spiral Settings", icon="FORCE_VORTEX")

        col.prop(props_owner, "spiral_count")
        col.prop(props_owner, "spiral_turns")
        col.prop(props_owner, "spiral_height")
        col.prop(props_owner, "spiral_start_radius")
        col.prop(props_owner, "spiral_end_radius")
        col.prop(props_owner, "spiral_axis")

    def get_default_parameters(self):
        """Параметры по умолчанию для Spiral Cloner"""
        base_defaults = super().get_default_parameters()
        spiral_defaults = {
            "count": 20,
            "turns": 3.0,
            "height": 5.0,
            "start_radius": 2.0,
            "end_radius": 2.0,
            "axis": (0.0, 0.0, 1.0)
        }
        return {**base_defaults, **spiral_defaults}


# Создаем глобальный экземпляр для использования в системе
spiral_cloner = SpiralCloner()


def register():
    """Регистрация Spiral клонера"""
    print("✅ Spiral Cloner registered")


def unregister():
    """Отмена регистрации Spiral клонера"""
    print("✅ Spiral Cloner unregistered")
